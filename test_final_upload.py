#!/usr/bin/env python3
"""
最终上传功能测试
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_complete_upload_flow():
    """测试完整的上传流程"""
    print("=== 测试完整的上传流程 ===")
    
    try:
        from app import create_app
        from io import BytesIO
        
        app = create_app()
        
        with app.test_client() as client:
            # 1. 登录
            print("1. 测试登录...")
            login_page = client.get('/login')
            
            import re
            page_content = login_page.get_data(as_text=True)
            csrf_match = re.search(r'name="csrf_token" type="hidden" value="([^"]+)"', page_content)
            
            if csrf_match:
                csrf_token = csrf_match.group(1)
                
                login_data = {
                    'csrf_token': csrf_token,
                    'username': 'admin',
                    'password': 'password123',
                    'submit': '登录'
                }
                
                login_response = client.post('/login', data=login_data, follow_redirects=False)
                
                if login_response.status_code == 302:
                    print("✓ 登录成功")
                    
                    # 2. 访问上传页面
                    print("2. 测试上传页面访问...")
                    upload_page = client.get('/upload')
                    
                    if upload_page.status_code == 200:
                        print("✓ 上传页面可访问")
                        
                        # 3. 提取上传页面CSRF token
                        upload_content = upload_page.get_data(as_text=True)
                        upload_csrf_match = re.search(r'name="csrf_token" type="hidden" value="([^"]+)"', upload_content)
                        
                        if upload_csrf_match:
                            upload_csrf_token = upload_csrf_match.group(1)
                            print("✓ 获取上传页面CSRF token成功")
                            
                            # 4. 准备测试文件
                            print("3. 准备测试文件...")
                            file_content = """第一章 穿越异世

李明睁开眼睛，发现自己躺在一个陌生的房间里。

"这是哪里？"他疑惑地坐起身来，环顾四周。

房间很简陋，只有一张木床、一张桌子和一把椅子。

第二章 神秘老者

门被推开，一个白发苍苍的老者走了进来。

"前辈，您是？"李明连忙起身行礼。

老者微笑着摆摆手："不必多礼。我叫张无忌。"

第三章 修炼之路

"我？"李明指着自己，满脸疑惑。

张无忌点点头："你体内有着极为罕见的灵根。"

"修真世界？灵根？"李明感觉自己的世界观正在被颠覆。

第四章 初次修炼

李明依言盘腿而坐，闭上眼睛，按照书中的方法开始冥想。

起初什么都感觉不到，但渐渐地，他似乎感受到了周围有一些细微的能量在流动。

"很好，你已经能够感知到灵气了。"

第五章 突破境界

一个月后，李明终于成功地将第一缕灵气引入体内。

正式踏上了修真之路，开始了新的人生。

这只是一个开始，更大的挑战还在后面等着他。
"""
                            
                            # 5. 提交上传
                            print("4. 提交文件上传...")
                            data = {
                                'csrf_token': upload_csrf_token,
                                'character': '李明',
                                'book_name': '修真传奇',
                                'channel': '男频',
                                'person': '三',
                                'submit': '开始优化',
                                'file': (BytesIO(file_content.encode('utf-8')), 'test_novel.txt', 'text/plain')
                            }
                            
                            upload_response = client.post('/upload', 
                                                        data=data, 
                                                        content_type='multipart/form-data')
                            
                            print(f"上传响应状态: {upload_response.status_code}")
                            
                            if upload_response.status_code == 302:
                                print("✓ 文件上传成功，重定向到任务详情页")
                                
                                # 提取任务ID
                                location = upload_response.location
                                task_id_match = re.search(r'/task/(\d+)', location)
                                if task_id_match:
                                    task_id = task_id_match.group(1)
                                    print(f"✓ 创建任务ID: {task_id}")
                                    
                                    # 6. 访问任务详情页
                                    print("5. 测试任务详情页...")
                                    task_page = client.get(f'/task/{task_id}')
                                    
                                    if task_page.status_code == 200:
                                        print("✓ 任务详情页可访问")
                                        
                                        # 检查页面内容
                                        task_content = task_page.get_data(as_text=True)
                                        if '修真传奇' in task_content and '李明' in task_content:
                                            print("✓ 任务信息显示正确")
                                        else:
                                            print("✗ 任务信息显示不正确")
                                            
                                        # 检查是否有进度显示
                                        if 'progress' in task_content.lower() or '进度' in task_content:
                                            print("✓ 包含进度显示")
                                        else:
                                            print("? 未发现明显的进度显示")
                                            
                                    else:
                                        print(f"✗ 任务详情页访问失败: {task_page.status_code}")
                                        
                                else:
                                    print("✗ 无法提取任务ID")
                                    
                            else:
                                print("✗ 文件上传失败")
                                response_text = upload_response.get_data(as_text=True)
                                
                                # 查找错误信息
                                if 'alert-danger' in response_text:
                                    error_match = re.search(r'alert-danger[^>]*>.*?([^<]+)', response_text)
                                    if error_match:
                                        print(f"错误信息: {error_match.group(1).strip()}")
                                
                        else:
                            print("✗ 无法获取上传页面CSRF token")
                    else:
                        print(f"✗ 上传页面访问失败: {upload_page.status_code}")
                else:
                    print(f"✗ 登录失败: {login_response.status_code}")
            else:
                print("✗ 无法获取登录CSRF token")
                
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_chapter_splitting():
    """测试章节分割功能"""
    print("\n=== 测试章节分割功能 ===")
    
    try:
        from app.utils.file_utils import split_text_into_chapters
        
        test_text = """第一章 穿越异世

李明睁开眼睛，发现自己躺在一个陌生的房间里。

第二章 神秘老者

门被推开，一个白发苍苍的老者走了进来。

第三章 修炼之路

"我？"李明指着自己，满脸疑惑。
"""
        
        chapters = split_text_into_chapters(test_text)
        print(f"识别到 {len(chapters)} 个章节")
        
        for chapter in chapters:
            print(f"章节 {chapter['number']}: {chapter['title']}")
            print(f"  内容长度: {len(chapter['content'])}字")
            print(f"  内容预览: {chapter['content'][:50]}...")
        
        # 验证结果
        assert len(chapters) == 3, f"应该识别到3个章节，实际识别到{len(chapters)}个"
        assert chapters[0]['title'] == '第一章 穿越异世', f"第一章标题不正确: {chapters[0]['title']}"
        assert chapters[1]['title'] == '第二章 神秘老者', f"第二章标题不正确: {chapters[1]['title']}"
        assert chapters[2]['title'] == '第三章 修炼之路', f"第三章标题不正确: {chapters[2]['title']}"
        
        print("✓ 章节分割功能正确")
        
    except Exception as e:
        print(f"章节分割测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_complete_upload_flow()
    test_chapter_splitting()
    print("\n=== 最终测试完成 ===")
