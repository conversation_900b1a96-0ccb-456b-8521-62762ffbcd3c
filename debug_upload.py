#!/usr/bin/env python3
"""
调试上传功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_upload_config():
    """测试上传配置"""
    print("=== 测试上传配置 ===")
    
    try:
        from app import create_app
        app = create_app()
        
        with app.app_context():
            print(f"UPLOAD_FOLDER: {app.config['UPLOAD_FOLDER']}")
            print(f"MAX_CONTENT_LENGTH: {app.config['MAX_CONTENT_LENGTH']}")
            print(f"ALLOWED_EXTENSIONS: {app.config['ALLOWED_EXTENSIONS']}")
            print(f"SECRET_KEY: {app.config['SECRET_KEY'][:10]}...")
            
            # 检查上传目录是否存在
            upload_dir = app.config['UPLOAD_FOLDER']
            if upload_dir.exists():
                print(f"✓ 上传目录存在: {upload_dir}")
            else:
                print(f"✗ 上传目录不存在: {upload_dir}")
                
            # 检查权限
            try:
                test_file = upload_dir / "test.txt"
                test_file.write_text("test")
                test_file.unlink()
                print("✓ 上传目录可写")
            except Exception as e:
                print(f"✗ 上传目录不可写: {e}")
                
    except Exception as e:
        print(f"配置测试失败: {e}")

def test_form_validation():
    """测试表单验证"""
    print("\n=== 测试表单验证 ===")
    
    try:
        from app import create_app
        from app.forms import UploadForm
        from werkzeug.datastructures import FileStorage
        from io import BytesIO
        
        app = create_app()
        
        with app.app_context():
            with app.test_request_context():
                # 创建模拟文件
                file_data = BytesIO(b"This is a test file content")
                file_storage = FileStorage(
                    stream=file_data,
                    filename="test.txt",
                    content_type="text/plain"
                )
                
                # 创建表单数据
                form_data = {
                    'character': '测试主角',
                    'book_name': '测试书名',
                    'channel': '男频',
                    'person': '三',
                    'submit': True
                }
                
                # 创建表单
                form = UploadForm(data=form_data)
                form.file.data = file_storage
                
                print(f"表单验证结果: {form.validate()}")
                if form.errors:
                    print(f"表单错误: {form.errors}")
                else:
                    print("✓ 表单验证通过")
                    
                # 测试文件验证
                from app.utils.file_utils import allowed_file
                print(f"文件扩展名检查: {allowed_file('test.txt')}")
                print(f"文件扩展名检查: {allowed_file('test.doc')}")
                
    except Exception as e:
        print(f"表单验证测试失败: {e}")

def test_file_upload():
    """测试文件上传功能"""
    print("\n=== 测试文件上传功能 ===")
    
    try:
        from app import create_app
        from app.utils.file_utils import save_uploaded_file
        from werkzeug.datastructures import FileStorage
        from io import BytesIO
        
        app = create_app()
        
        with app.app_context():
            # 创建模拟文件
            file_content = """第一章 测试开始

这是一个测试文件的内容。
主角开始了他的冒险之旅。

第二章 测试继续

故事继续发展。
主角遇到了新的挑战。
"""
            file_data = BytesIO(file_content.encode('utf-8'))
            file_storage = FileStorage(
                stream=file_data,
                filename="test_novel.txt",
                content_type="text/plain"
            )
            
            # 测试保存文件
            user_id = 1
            file_path = save_uploaded_file(file_storage, user_id)
            
            if file_path:
                print(f"✓ 文件保存成功: {file_path}")
                
                # 验证文件内容
                with open(file_path, 'r', encoding='utf-8') as f:
                    saved_content = f.read()
                    if saved_content == file_content:
                        print("✓ 文件内容正确")
                    else:
                        print("✗ 文件内容不匹配")
                        
                # 测试章节分割
                from app.utils.file_utils import split_text_into_chapters
                chapters = split_text_into_chapters(saved_content)
                print(f"✓ 识别到 {len(chapters)} 个章节")
                for chapter in chapters:
                    print(f"  - {chapter['title']}: {len(chapter['content'])}字")
                    
                # 清理测试文件
                os.remove(file_path)
                print("✓ 测试文件已清理")
            else:
                print("✗ 文件保存失败")
                
    except Exception as e:
        print(f"文件上传测试失败: {e}")

def test_database():
    """测试数据库连接"""
    print("\n=== 测试数据库连接 ===")
    
    try:
        from app import create_app, db
        from app.models import User, AdaptationTask
        
        app = create_app()
        
        with app.app_context():
            # 测试数据库连接
            try:
                users = User.query.all()
                print(f"✓ 数据库连接正常，用户数量: {len(users)}")
                
                tasks = AdaptationTask.query.all()
                print(f"✓ 任务表正常，任务数量: {len(tasks)}")
                
            except Exception as e:
                print(f"✗ 数据库查询失败: {e}")
                
    except Exception as e:
        print(f"数据库测试失败: {e}")

if __name__ == "__main__":
    test_upload_config()
    test_form_validation()
    test_file_upload()
    test_database()
    print("\n=== 调试完成 ===")
