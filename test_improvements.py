#!/usr/bin/env python3
"""
测试故事优化功能的改进
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.utils.file_utils import split_text_into_chapters

def test_chapter_splitting():
    """测试章节分割功能"""
    print("=== 测试章节分割功能 ===")
    
    # 测试用例1：标准章节格式
    test_text1 = """
第一章 开始的故事

这是第一章的内容，讲述了主角的开始。
主角在这里遇到了第一个挑战。

第二章 继续冒险

第二章的内容继续讲述主角的冒险。
这里有更多的情节发展。

第三章 高潮部分

第三章是故事的高潮部分。
主角面临最大的挑战。
"""
    
    chapters1 = split_text_into_chapters(test_text1)
    print(f"测试用例1 - 标准格式：识别到 {len(chapters1)} 个章节")
    for chapter in chapters1:
        print(f"  章节 {chapter['number']}: {chapter['title'][:20]}...")
    
    # 测试用例2：数字章节格式
    test_text2 = """
第1章 数字开始

这是用数字标记的第一章。

第2章 数字继续

这是第二章的内容。

第3章 数字结束

这是第三章的内容。
"""
    
    chapters2 = split_text_into_chapters(test_text2)
    print(f"\n测试用例2 - 数字格式：识别到 {len(chapters2)} 个章节")
    for chapter in chapters2:
        print(f"  章节 {chapter['number']}: {chapter['title'][:20]}...")
    
    # 测试用例3：无章节标题的长文本
    test_text3 = """
这是一个很长的故事，没有明确的章节标题。

第一个段落讲述了主角的背景。主角是一个普通的学生，生活在一个小城市里。

第二个段落描述了一个转折点。主角突然发现自己有了特殊的能力。

第三个段落是冒险的开始。主角决定离开家乡，去寻找答案。

第四个段落讲述了路上的遭遇。主角遇到了各种各样的人和事。

第五个段落是故事的高潮。主角终于找到了真相。

第六个段落是结局。主角回到了家乡，但已经不是原来的自己了。
"""
    
    chapters3 = split_text_into_chapters(test_text3)
    print(f"\n测试用例3 - 无标题格式：识别到 {len(chapters3)} 个章节")
    for chapter in chapters3:
        print(f"  章节 {chapter['number']}: {chapter['title'][:20]}... (长度: {len(chapter['content'])}字)")

def test_form_choices():
    """测试表单选项顺序"""
    print("\n=== 测试表单选项顺序 ===")

    # 直接检查表单源码
    with open('app/forms.py', 'r', encoding='utf-8') as f:
        forms_content = f.read()

    # 查找人称选项定义
    import re
    person_pattern = r"person = SelectField\('人称', choices=\[\s*\('([^']+)', '[^']+'\),\s*\('([^']+)', '[^']+'\)"
    matches = re.findall(person_pattern, forms_content)

    if matches:
        for i, match in enumerate(matches):
            first_choice, second_choice = match
            print(f"表单 {i+1} 人称选项顺序: {first_choice} -> {second_choice}")
            if first_choice == '三':
                print(f"✓ 表单 {i+1} 人称选项顺序正确（第三人称在前）")
            else:
                print(f"✗ 表单 {i+1} 人称选项顺序不正确")
    else:
        print("未找到人称选项定义")

if __name__ == "__main__":
    test_chapter_splitting()
    test_form_choices()
    print("\n=== 所有测试完成 ===")
